import { ThresholdStrategy } from '../strategies/ThresholdStrategy'
import { TradingStrategy } from '../strategies/TradingStrategy'

export class StrategyFactory {
  static createStrategy(type: StrategyType, config: StrategyConfig): TradingStrategy {
    switch (type) {
      case 'threshold':
        return new ThresholdStrategy(config)
      // Add more cases for other strategies
      default:
        throw new Error(`Strategy ${name} not found`)
    }
  }

  static getAvailableStrategies(): Array<{
    type: StrategyType
    name: string
    description: string
  }> {
    return [
      {
        type: 'threshold',
        name: 'Threshold Strategy',
        description:
          'Advanced threshold strategy with momentum analysis, volatility filtering, and trend consistency checks.'
      }
    ]
  }

  static getDefaultConfig(type: StrategyType): StrategyConfig {
    switch (type) {
      case 'threshold':
        return {
          threshold: 0.02,
          minConfidence: 0.4,
          volatilityFilter: true,
          momentumConfirmation: true,
          consistencyCheck: true
        }
      // Add more cases for other strategies
      default:
        throw new Error(`Strategy ${name} not found`)
    }
  }
}
