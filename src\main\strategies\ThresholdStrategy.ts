import { Logger } from '../../shared/utils/Logger'
import { ATRStateful } from '../inicators/ATRStateful'
import { TradingStrategy } from './TradingStrategy'

const logger = Logger.getContextLogger('THRESHOLD')

interface ThresholdAnalysis {
  changePercent: number
  momentum: 'strong' | 'moderate' | 'weak'
  consistency: number // 0-1 scale
  direction: 'up' | 'down' | 'neutral'
}

const MIN_THRESHOLD = 0.0001 // 0.01% minimum threshold

export class ThresholdStrategy extends TradingStrategy {
  private lastSignalBarTime: number | null = null
  private atrCalculator: ATRStateful

  constructor(config: StrategyConfig) {
    super(config)

    const atrPeriod = this.config.atrPeriod ?? 14
    this.atrCalculator = new ATRStateful(atrPeriod)
  }

  getName(): string {
    return 'Enhanced Threshold Strategy'
  }

  getDescription(): string {
    return 'Advanced threshold strategy with momentum analysis, volatility filtering, and trend consistency checks.'
  }

  getCandleCount(): number {
    return this.priceHistory.length
  }

  async evaluate(candle: Candle): Promise<TradingDecision> {
    this.addCandle(candle)

    if (this.priceHistory.length < 2) {
      return {
        shouldTrade: false,
        reason: 'Not enough data to evaluate',
        confidence: 0
      }
    }

    // Sort the price history by time
    this.priceHistory.sort((a, b) => a.time - b.time)

    const prevCandle = this.priceHistory[this.priceHistory.length - 2]

    const atr = this.atrCalculator.update(candle, prevCandle.close)
    if (atr === null) {
      return { shouldTrade: false, reason: 'ATR not yet initialized' }
    }

    const periodSec = this.config.candlePeriodSeconds!
    if (periodSec === undefined) {
      throw new Error('ThresholdStrategy: missing `candlePeriodSeconds` in config')
    }

    const minConfidence = this.config.minConfidence ?? 0.5
    const volatilityFilter = this.config.volatilityFilter ?? true
    const momentumConfirmation = this.config.momentumConfirmation ?? true
    const consistencyCheck = this.config.consistencyCheck ?? true

    // ↓↓↓ new ATR-scaled threshold ↓↓↓
    const atrPct = atr / candle.open
    const atrMultiplier = this.config.atrMultiplier ?? 0.5
    // Use ATR-based threshold (percentage)
    const dynamicThresh = atrPct * atrMultiplier
    const minThreshold = this.config.minThresholdPct ?? MIN_THRESHOLD
    const threshold = Math.max(dynamicThresh, minThreshold)
    const analysis = this.analyzeThreshold(candle, threshold)

    logger.debug(
      `ATR(${this.atrCalculator.getPeriod()})=${(atrPct * 100).toFixed(4)}% → dynamicThresh=${(dynamicThresh * 100).toFixed(4)}% → minThresh=${(minThreshold * 100).toFixed(4)}% → threshold ${(threshold * 100).toFixed(4)}%, multiplier=${atrMultiplier}`
    )
    if (volatilityFilter && !this.passedVolatilityFilter(atrPct)) {
      return {
        shouldTrade: false,
        reason: `Volatility ${(atrPct * 100).toFixed(2)}% exceeds max allowed, trade filtered`
      }
    }

    const barTime = candle.time

    // Check basic threshold condition
    if (Math.abs(analysis.changePercent) < threshold) {
      logger.debug(
        `Price change ${(analysis.changePercent * 100).toFixed(6)}% is below threshold ${(threshold * 100).toFixed(6)}% - HOLD`
      )
      return {
        shouldTrade: false,
        reason: `Price change ${(analysis.changePercent * 100).toFixed(3)}% is below threshold ${(threshold * 100).toFixed(3)}%`
      }
    }

    // Check threshold condition FIRST (or already signaled this bar)
    if (this.lastSignalBarTime === barTime) {
      return {
        shouldTrade: false,
        reason: 'already signaled this bar'
      }
    }

    this.lastSignalBarTime = barTime

    // Calculate the expiry based on the threshold and chart period
    const rawExpiry = periodSec * (threshold / Math.abs(analysis.changePercent))
    let expiry = Math.max(5, Math.min(periodSec, Math.round(rawExpiry / 5) * 5))

    // Use the user-defined expiry if provided
    if (this.config.expiryToSeconds !== undefined) {
      expiry = this.config.expiryToSeconds
    }

    // Generate trading decision based on analysis
    const decision = this.generateTradingDecision(
      analysis,
      threshold,
      minConfidence,
      momentumConfirmation,
      consistencyCheck,
      expiry
    )

    logger.debug(
      `ATR(${this.atrCalculator.getPeriod()})=${(atrPct * 100).toFixed(4)}% → threshold ${(threshold * 100).toFixed(4)}%, confidence=${((decision.confidence ?? 0) * 100).toFixed(1)}%, expiry=${expiry}`
    )
    return decision
  }

  private analyzeThreshold(candle: Candle, threshold: number): ThresholdAnalysis {
    const currentPrice = candle.close
    const previousPrice = this.priceHistory[this.priceHistory.length - 2].close

    // Directional change percent
    const changePercent = (currentPrice - previousPrice) / previousPrice

    // Determine momentum strength
    let momentum: 'strong' | 'moderate' | 'weak' = 'weak'
    const absChange = Math.abs(changePercent)
    if (absChange >= threshold * 3) {
      momentum = 'strong'
    } else if (absChange >= threshold * 1.5) {
      momentum = 'moderate'
    }

    const trend =
      currentPrice > previousPrice ? 'up' : currentPrice < previousPrice ? 'down' : 'neutral'

    // Calculate trend consistency
    const consistency = this.checkTrendConsistency(trend)

    return {
      changePercent,
      // volatility,
      momentum,
      consistency,
      direction: trend
    }
  }

  private generateTradingDecision(
    analysis: ThresholdAnalysis,
    threshold: number,
    minConfidence: number,
    momentumConfirmation: boolean,
    consistencyCheck: boolean,
    expiry: number
  ): TradingDecision {
    const direction = analysis.direction === 'up' ? 'high' : 'low'

    // --- START OF NEW TREND LOGIC ---
    const trendSmaPeriod = 20 // A common period for trend, make this configurable!
    const sma = this.calculateSMA(trendSmaPeriod)
    let isWithTrend = false
    if (sma !== null) {
      if (direction === 'high' && this.priceHistory[this.priceHistory.length - 1].close > sma) {
        isWithTrend = true
      }
      if (direction === 'low' && this.priceHistory[this.priceHistory.length - 1].close < sma) {
        isWithTrend = true
      }
    }

    // Apply all modifiers first, then clamp once at the end
    let confidence = threshold > 0 ? Math.min(1, Math.abs(analysis.changePercent) / threshold) : 0
    const baseConfidence = confidence

    // Momentum modifier
    if (momentumConfirmation) {
      if (analysis.momentum === 'strong') confidence += 0.3
      else if (analysis.momentum === 'moderate') confidence += 0.2
      else confidence -= 0.2
    }

    // Consistency modifier
    if (consistencyCheck) {
      confidence += (analysis.consistency - 0.5) * 0.4 // Reduced multiplier
    }

    logger.debug(
      `Confidence calc: base=${(baseConfidence * 100).toFixed(1)}%, momentum=${analysis.momentum}, consistency=${(analysis.consistency * 100).toFixed(1)}%`
    )

    let reason = `Price change ${(analysis.changePercent * 100).toFixed(3)}% exceeds threshold ${(threshold * 100).toFixed(3)}%`

    // --- NEW: POWERFUL TREND MODIFIER ---
    if (isWithTrend) {
      confidence += 0.3 // Give a BIG boost if the trade is with the trend
      reason += ` (Trend Confirmation ✔️)`
    } else if (sma !== null) {
      confidence -= 0.4 // Heavily penalize trades against the trend
      reason += ` (Against Trend ❌)`
    }

    logger.debug(
      `Trend analysis: isWithTrend=${isWithTrend}, sma=${sma?.toFixed(5)}, currentPrice=${this.priceHistory[this.priceHistory.length - 1].close}, finalConfidence=${(confidence * 100).toFixed(1)}%`
    )

    // Limit confidence to 0-1 scale
    confidence = Math.max(0, Math.min(1, confidence))

    // After all confidence modifications
    if (isNaN(confidence)) {
      confidence = 0
      logger.warn(
        `NaN confidence detected! Change: ${analysis.changePercent}, Threshold: ${threshold}`
      )
    }

    reason += `(momentum: ${analysis.momentum}, consistency: ${analysis.consistency * 100}%)`
    reason += `- confidence: ${(confidence * 100).toFixed(1)}%`

    // Final decision
    const shouldTrade = confidence >= minConfidence

    if (!shouldTrade) {
      return {
        shouldTrade: false,
        reason: `Confidence ${(confidence * 100).toFixed(1)}% is below minimum ${(minConfidence * 100).toFixed(1)}%`
      }
    }

    return {
      shouldTrade,
      direction,
      confidence,
      reason,
      expirySeconds: expiry
    }
  }

  // Use the ATRp you already calculated! It's the best measure here.
  private passedVolatilityFilter(atrPct: number): boolean {
    const maxVolatilityPct = this.config.maxVolatilityPercent ?? 5.0
    // atrPct is already a percentage if you multiply by 100
    return atrPct * 100 <= maxVolatilityPct
  }

  private checkTrendConsistency(trend: 'up' | 'down' | 'neutral'): number {
    if (this.priceHistory.length < 5) return 0.5

    const recentHistory = this.priceHistory.slice(-5)
    let consistentTrends = 0
    for (let i = 1; i < recentHistory.length; i++) {
      const currentCandle = recentHistory[i]
      const prevCandle = recentHistory[i - 1]
      const currentTrend = currentCandle.close > prevCandle.close ? 'up' : 'down'
      if (currentTrend === trend) {
        consistentTrends++
      }
    }

    // Compare against the 4 possible trend changes in a 5-candle series
    return consistentTrends / (recentHistory.length - 1)
  }
}
