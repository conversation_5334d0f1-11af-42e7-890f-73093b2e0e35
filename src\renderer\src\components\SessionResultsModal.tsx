import { useState, useEffect } from 'react'

interface SessionResultsModalProps {
  isOpen: boolean
  onClose: () => void
  sessionResult: SessionResult | null
}

const SessionResultsModal: React.FC<SessionResultsModalProps> = ({
  isOpen,
  onClose,
  sessionResult
}) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true)
    }
  }, [isOpen])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(onClose, 300) // Wait for animation to complete
  }

  if (!isOpen || !sessionResult) return null

  const isProfitable = sessionResult.profitable
  const successRate = sessionResult.successRate
  const duration = Math.round(
    (sessionResult.endTime.getTime() - sessionResult.startTime.getTime()) / 1000 / 60
  ) // minutes

  const getEndReasonText = (reason: string) => {
    switch (reason) {
      case 'target_reached':
        return 'Target profit reached! 🎯'
      case 'capital_depleted':
        return 'Capital protection activated 🛡️'
      case 'manual_stop':
        return 'Manually stopped'
      default:
        return 'Session ended'
    }
  }

  const getEmoji = () => {
    if (isProfitable) {
      if (sessionResult.endReason === 'target_reached') {
        return '🎉🎯💰'
      }
      return '✅💚📈'
    } else {
      return '📉💔🔄'
    }
  }

  const getMessage = () => {
    if (isProfitable) {
      if (sessionResult.endReason === 'target_reached') {
        return "Congratulations! You've reached your target profit!"
      }
      return 'Great job! Your session ended with a profit!'
    } else {
      return "Don't worry! Every trader faces losses. Learn from this session and come back stronger!"
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        className={`bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 transform transition-all duration-300 ${
          isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        }`}
      >
        {/* Header */}
        <div className="text-center mb-6">
          <div className="text-4xl mb-2">{getEmoji()}</div>
          <h2 className={`text-2xl font-bold ${isProfitable ? 'text-green-400' : 'text-red-400'}`}>
            Session Complete
          </h2>
          <p className="text-gray-300 mt-2">{getMessage()}</p>
        </div>

        {/* End Reason */}
        <div className="text-center mb-6">
          <p className="text-lg font-semibold text-blue-400">
            {getEndReasonText(sessionResult.endReason)}
          </p>
        </div>

        {/* Statistics */}
        <div className="space-y-4 mb-6">
          {/* Profit/Loss */}
          <div className="flex justify-between items-center p-3 bg-gray-700 rounded">
            <span className="text-gray-300">Total Profit/Loss:</span>
            <span
              className={`font-bold text-lg ${sessionResult.totalProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}
            >
              ${sessionResult.totalProfit >= 0 ? '+' : ''}${sessionResult.totalProfit.toFixed(2)}
            </span>
          </div>

          {/* Capital */}
          <div className="flex justify-between items-center p-3 bg-gray-700 rounded">
            <span className="text-gray-300">Final Capital:</span>
            <span className="font-bold text-white">${sessionResult.finalCapital.toFixed(2)}</span>
          </div>

          {/* Trades */}
          <div className="grid grid-cols-3 gap-2">
            <div className="text-center p-2 bg-gray-700 rounded">
              <div className="text-sm text-gray-400">Total Trades</div>
              <div className="font-bold text-white">{sessionResult.totalTrades}</div>
            </div>
            <div className="text-center p-2 bg-gray-700 rounded">
              <div className="text-sm text-gray-400">Wins</div>
              <div className="font-bold text-green-400">{sessionResult.winCount}</div>
            </div>
            <div className="text-center p-2 bg-gray-700 rounded">
              <div className="text-sm text-gray-400">Losses</div>
              <div className="font-bold text-red-400">{sessionResult.lossCount}</div>
            </div>
          </div>

          {/* Success Rate */}
          <div className="flex justify-between items-center p-3 bg-gray-700 rounded">
            <span className="text-gray-300">Success Rate:</span>
            <span
              className={`font-bold ${successRate >= 60 ? 'text-green-400' : successRate >= 40 ? 'text-yellow-400' : 'text-red-400'}`}
            >
              {successRate.toFixed(1)}%
            </span>
          </div>

          {/* Duration */}
          <div className="flex justify-between items-center p-3 bg-gray-700 rounded">
            <span className="text-gray-300">Session Duration:</span>
            <span className="font-bold text-white">
              {duration} minute{duration !== 1 ? 's' : ''}
            </span>
          </div>

          {/* Strategy */}
          <div className="flex justify-between items-center p-3 bg-gray-700 rounded">
            <span className="text-gray-300">Strategy Used:</span>
            <span className="font-bold text-blue-400 capitalize">{sessionResult.strategy}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={handleClose}
            className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded transition-colors"
          >
            Close
          </button>
          <button
            onClick={() => {
              // Could add functionality to start a new session
              handleClose()
            }}
            className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
          >
            New Session
          </button>
        </div>
      </div>
    </div>
  )
}

export default SessionResultsModal
